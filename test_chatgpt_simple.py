#!/usr/bin/env python
"""
Script simple para probar ChatGPT
"""

def test_chatgpt():
    print("PROBANDO CHATGPT INTEGRATION")
    print("=" * 40)
    
    try:
        from tr_partes.external_ai_service import external_ai_service
        print("Servicio importado correctamente")
        
        # Verificar configuracion
        print(f"OpenAI API Key configurada: {'SI' if external_ai_service.openai_api_key else 'NO'}")
        print(f"URL: {external_ai_service.openai_url}")
        
        # Verificar prompt
        prompt_content = external_ai_service._load_prompt(external_ai_service.chatgpt_prompt_file)
        print(f"Prompt ChatGPT cargado: {'SI' if prompt_content else 'NO'}")
        
        if prompt_content:
            print(f"Longitud del prompt: {len(prompt_content)} caracteres")
        
        print("\nOpciones de IA disponibles:")
        print("1 = Gemini 2.0 Flash")
        print("2 = Mistral Small") 
        print("3 = ChatGPT 4o-mini")
        
        print("\nPara usar ChatGPT:")
        print("POST /api/partes/analizar-ia/ con {'ia': 3}")
        print("POST /api/partes/consultar-ia/ con {'ia': 3}")
        
        print("\nINTEGRACION COMPLETADA EXITOSAMENTE!")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_chatgpt()
