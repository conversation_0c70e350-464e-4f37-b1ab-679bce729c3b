from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from django.conf import settings
from .services import aplicar_cierre_automatico_y_obtener_partes, ParteAutoCloseService
import logging
import os
from django.http import HttpResponse, Http404
from django.utils import timezone
from datetime import timedelta
import mimetypes

logger = logging.getLogger(__name__)

def generate_file_path(parte, filename):
    """
    Genera la ruta de archivo siguiendo la estructura:
    /media/partes/documento_usuario/nombre_incidencia/id_parte/

    Args:
        parte: Instancia de TrParte
        filename: Nombre del archivo original

    Returns:
        tuple: (upload_dir, file_path, unique_filename)
    """
    import uuid

    # Obtener información del parte
    documento_usuario = parte.int_idUsuario.str_documento

    # Obtener nombre de incidencia (si existe)
    if parte.int_idIncidencia:
        nombre_incidencia = parte.int_idIncidencia.str_codigoIncidencia
    else:
        nombre_incidencia = "sin_incidencia"

    id_parte = str(parte.int_idParte)

    # Crear directorio con la estructura especificada
    upload_dir = f"partes/{documento_usuario}/{nombre_incidencia}/{id_parte}/"

    # Generar nombre único para el archivo
    file_extension = os.path.splitext(filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(upload_dir, unique_filename)

    return upload_dir, file_path, unique_filename
from .models import TrParte, CorrelativoParte, TrArchivo
from .serializers import (
    TrParteSerializer,
    TrParteCreateSerializer,
    TrParteUpdateSerializer,
    CorrelativoParteSerializer,
    TrArchivoSerializer,
    TrArchivoUploadSerializer
)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def partes_list_create(request):
    """
    Lista todos los partes o crea uno nuevo
    GET: Lista partes con filtros opcionales
    POST: Crea un nuevo parte
    """
    if request.method == 'GET':
        # Parámetros de paginación
        page = int(request.query_params.get('page', 1))
        limit = int(request.query_params.get('limit', 10))

        # Validar parámetros de paginación
        if page < 1:
            page = 1
        if limit < 1 or limit > 100:  # Máximo 100 por página
            limit = 10

        # Calcular offset
        offset = (page - 1) * limit

        # Obtener parámetros de consulta para filtrado
        usuario = request.query_params.get('usuario', None)
        fecha_desde = request.query_params.get('fecha_desde', None)
        fecha_hasta = request.query_params.get('fecha_hasta', None)

        # Nuevos parámetros de búsqueda
        tipo_incidencia = request.query_params.get('tipo_incidencia', None)
        codigo_parte = request.query_params.get('codigo_parte', None)
        buscar = request.query_params.get('buscar', None)  # Búsqueda general

        # Iniciar el queryset base con relaciones - INCLUIR TODOS LOS PARTES (activos e inactivos)
        partes_query = TrParte.objects.select_related(
            'int_idUsuario',
            'int_idIncidencia',
            'int_idDireccion',
            'int_idUsuarioCreacion'
        ).all()

        # Aplicar cierre automático antes de filtrar
        partes_query, resultado_cierre = aplicar_cierre_automatico_y_obtener_partes(partes_query)

        # Aplicar filtros si se proporcionan
        if usuario:
            partes_query = partes_query.filter(int_idUsuario=usuario)
        if fecha_desde:
            from datetime import datetime, time
            fecha_desde_dt = datetime.strptime(fecha_desde, '%Y-%m-%d')
            fecha_desde_dt = fecha_desde_dt.replace(hour=0, minute=0, second=0, microsecond=0)
            partes_query = partes_query.filter(dt_fechaIncidente__gte=fecha_desde_dt)
        if fecha_hasta:
            from datetime import datetime, time
            fecha_hasta_dt = datetime.strptime(fecha_hasta, '%Y-%m-%d')
            fecha_hasta_dt = fecha_hasta_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
            partes_query = partes_query.filter(dt_fechaIncidente__lte=fecha_hasta_dt)

        # Filtro por tipo de incidencia
        if tipo_incidencia:
            partes_query = partes_query.filter(int_idIncidencia__str_descripcionCorta__icontains=tipo_incidencia)

        # Filtro por código de parte
        if codigo_parte:
            partes_query = partes_query.filter(str_codigoParte__icontains=codigo_parte)

        # Búsqueda general (en descripción, código de parte o tipo de incidencia)
        if buscar:
            from django.db.models import Q
            partes_query = partes_query.filter(
                Q(tx_descripcion__icontains=buscar) |
                Q(str_codigoParte__icontains=buscar) |
                Q(int_idIncidencia__str_descripcionCorta__icontains=buscar)
            )

        # Ordenar por fecha de creación (más reciente primero)
        partes_query = partes_query.order_by('-dt_fechaRegistro', '-int_idParte')

        # Contar total de partes
        total_partes = partes_query.count()

        # Aplicar paginación
        partes = partes_query[offset:offset + limit]

        # Serializar datos
        serializer = TrParteSerializer(partes, many=True)

        # Calcular información de paginación
        total_pages = (total_partes + limit - 1) // limit  # Redondeo hacia arriba
        has_next = page < total_pages
        has_previous = page > 1

        return Response({
            'total': total_partes,
            'partes': serializer.data,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'limit': limit,
                'has_next': has_next,
                'has_previous': has_previous,
                'next_page': page + 1 if has_next else None,
                'previous_page': page - 1 if has_previous else None
            },
            'filters_applied': {
                'usuario': usuario,
                'fecha_desde': fecha_desde,
                'fecha_hasta': fecha_hasta,
                'tipo_incidencia': tipo_incidencia,
                'codigo_parte': codigo_parte,
                'buscar': buscar
            }
        })

    elif request.method == 'POST':
        serializer = TrParteCreateSerializer(data=request.data)
        if serializer.is_valid():
            # int_idUsuario viene del body, int_idUsuarioCreacion se asigna automáticamente
            parte = serializer.save(int_idUsuarioCreacion=request.user)

            # Devolver el parte completo con información anidada
            response_serializer = TrParteSerializer(parte)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def parte_detail(request, pk):
    """
    Obtiene, actualiza o elimina un parte específico
    """
    try:
        parte = TrParte.objects.select_related('int_idUsuario', 'int_idIncidencia', 'int_idDireccion').get(pk=pk)
    except TrParte.DoesNotExist:
        return Response(
            {'error': 'Parte no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )

    if request.method == 'GET':
        serializer = TrParteSerializer(parte)
        return Response(serializer.data)

    elif request.method == 'PUT':
        # Verificar permisos: el creador, admin o supervisor pueden actualizar
        if (request.user != parte.int_idUsuario and
            request.user.str_rol not in ['ADMIN', 'SUPERVISOR']):
            return Response(
                {'error': 'No tiene permisos para actualizar este parte'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = TrParteUpdateSerializer(parte, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            # Devolver el parte actualizado con información completa
            response_serializer = TrParteSerializer(parte)
            return Response(response_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    elif request.method == 'DELETE':
        # Verificar permisos: solo admin puede eliminar
        if request.user.str_rol not in ['ADMIN']:
            return Response(
                {'error': 'No tiene permisos para eliminar partes'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Eliminar físicamente el parte
        parte.delete()
        return Response(
            {'message': 'Parte eliminado correctamente'},
            status=status.HTTP_204_NO_CONTENT
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def buscar_partes(request):
    """
    Busca partes por descripción o código
    """
    from django.db.models import Q

    query = request.query_params.get('q', '')

    if not query:
        return Response(
            {'error': 'Parámetro de búsqueda "q" es requerido'},
            status=status.HTTP_400_BAD_REQUEST
        )

    partes = TrParte.objects.select_related('int_idUsuario', 'int_idIncidencia', 'int_idDireccion').filter(
        Q(tx_descripcion__icontains=query) |
        Q(str_codigoParte__icontains=query)
    ).filter(int_estado=1).order_by('-dt_fechaIncidente')

    serializer = TrParteSerializer(partes, many=True)

    return Response({
        'total': partes.count(),
        'partes': serializer.data,
        'query': query
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def mis_partes(request):
    """
    Obtiene los partes creados por el usuario actual con paginación y filtros de búsqueda
    Incluye todos los partes (activos e inactivos) ordenados por fecha de creación
    """
    # Parámetros de paginación
    page = int(request.GET.get('page', 1))
    limit = int(request.GET.get('limit', 10))

    # Validar parámetros
    if page < 1:
        page = 1
    if limit < 1 or limit > 100:  # Máximo 100 por página
        limit = 10

    # Calcular offset
    offset = (page - 1) * limit

    # Obtener parámetros de búsqueda
    fecha_desde = request.query_params.get('fecha_desde', None)
    fecha_hasta = request.query_params.get('fecha_hasta', None)
    tipo_incidencia = request.query_params.get('tipo_incidencia', None)
    codigo_parte = request.query_params.get('codigo_parte', None)
    buscar = request.query_params.get('buscar', None)

    # Obtener TODOS los partes del usuario actual (activos e inactivos)
    partes_query = TrParte.objects.select_related(
        'int_idUsuario', 'int_idIncidencia', 'int_idDireccion', 'int_idUsuarioCreacion'
    ).filter(int_idUsuario=request.user)

    # Aplicar cierre automático antes de filtrar
    partes_query, resultado_cierre = aplicar_cierre_automatico_y_obtener_partes(partes_query)

    # Aplicar filtros de búsqueda
    if fecha_desde:
        from datetime import datetime
        fecha_desde_dt = datetime.strptime(fecha_desde, '%Y-%m-%d')
        fecha_desde_dt = fecha_desde_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        partes_query = partes_query.filter(dt_fechaIncidente__gte=fecha_desde_dt)
    if fecha_hasta:
        from datetime import datetime
        fecha_hasta_dt = datetime.strptime(fecha_hasta, '%Y-%m-%d')
        fecha_hasta_dt = fecha_hasta_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        partes_query = partes_query.filter(dt_fechaIncidente__lte=fecha_hasta_dt)
    if tipo_incidencia:
        partes_query = partes_query.filter(int_idIncidencia__str_descripcionCorta__icontains=tipo_incidencia)
    if codigo_parte:
        partes_query = partes_query.filter(str_codigoParte__icontains=codigo_parte)
    if buscar:
        from django.db.models import Q
        partes_query = partes_query.filter(
            Q(tx_descripcion__icontains=buscar) |
            Q(str_codigoParte__icontains=buscar) |
            Q(int_idIncidencia__str_descripcionCorta__icontains=buscar)
        )

    # Ordenar por fecha de creación (más reciente primero)
    partes_query = partes_query.order_by('-dt_fechaRegistro', '-int_idParte')

    # Contar total de partes
    total_partes = partes_query.count()

    # Aplicar paginación
    partes = partes_query[offset:offset + limit]

    # Serializar datos
    serializer = TrParteSerializer(partes, many=True)

    # Calcular información de paginación
    total_pages = (total_partes + limit - 1) // limit  # Redondeo hacia arriba
    has_next = page < total_pages
    has_previous = page > 1

    return Response({
        'total': total_partes,
        'partes': serializer.data,
        'pagination': {
            'current_page': page,
            'total_pages': total_pages,
            'limit': limit,
            'has_next': has_next,
            'has_previous': has_previous,
            'next_page': page + 1 if has_next else None,
            'previous_page': page - 1 if has_previous else None
        },
        'filters_applied': {
            'fecha_desde': fecha_desde,
            'fecha_hasta': fecha_hasta,
            'tipo_incidencia': tipo_incidencia,
            'codigo_parte': codigo_parte,
            'buscar': buscar
        }
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def correlativos_usuario(request):
    """
    Obtiene los correlativos del usuario actual
    """
    correlativos = CorrelativoParte.objects.filter(
        int_idUsuario=request.user
    ).order_by('-int_año', '-int_mes')

    serializer = CorrelativoParteSerializer(correlativos, many=True)

    return Response({
        'total': correlativos.count(),
        'correlativos': serializer.data
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def partes_por_usuario(request, usuario_id):
    """
    Obtiene los partes de un usuario específico con paginación y filtros de búsqueda
    Incluye todos los partes (activos e inactivos) ordenados por fecha de creación
    """
    # Parámetros de paginación
    page = int(request.GET.get('page', 1))
    limit = int(request.GET.get('limit', 10))

    # Validar parámetros
    if page < 1:
        page = 1
    if limit < 1 or limit > 100:  # Máximo 100 por página
        limit = 10

    # Calcular offset
    offset = (page - 1) * limit

    # Obtener parámetros de búsqueda
    fecha_desde = request.query_params.get('fecha_desde', None)
    fecha_hasta = request.query_params.get('fecha_hasta', None)
    tipo_incidencia = request.query_params.get('tipo_incidencia', None)
    codigo_parte = request.query_params.get('codigo_parte', None)
    buscar = request.query_params.get('buscar', None)

    # Obtener TODOS los partes del usuario (activos e inactivos)
    partes_query = TrParte.objects.select_related(
        'int_idUsuario', 'int_idIncidencia', 'int_idDireccion', 'int_idUsuarioCreacion'
    ).filter(int_idUsuario=usuario_id)

    # Aplicar cierre automático antes de filtrar
    partes_query, resultado_cierre = aplicar_cierre_automatico_y_obtener_partes(partes_query)

    # Aplicar filtros de búsqueda
    if fecha_desde:
        from datetime import datetime
        fecha_desde_dt = datetime.strptime(fecha_desde, '%Y-%m-%d')
        fecha_desde_dt = fecha_desde_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        partes_query = partes_query.filter(dt_fechaIncidente__gte=fecha_desde_dt)
    if fecha_hasta:
        from datetime import datetime
        fecha_hasta_dt = datetime.strptime(fecha_hasta, '%Y-%m-%d')
        fecha_hasta_dt = fecha_hasta_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        partes_query = partes_query.filter(dt_fechaIncidente__lte=fecha_hasta_dt)
    if tipo_incidencia:
        partes_query = partes_query.filter(int_idIncidencia__str_descripcionCorta__icontains=tipo_incidencia)
    if codigo_parte:
        partes_query = partes_query.filter(str_codigoParte__icontains=codigo_parte)
    if buscar:
        from django.db.models import Q
        partes_query = partes_query.filter(
            Q(tx_descripcion__icontains=buscar) |
            Q(str_codigoParte__icontains=buscar) |
            Q(int_idIncidencia__str_descripcionCorta__icontains=buscar)
        )

    # Ordenar por fecha de creación (más reciente primero)
    partes_query = partes_query.order_by('-dt_fechaRegistro', '-int_idParte')

    # Contar total de partes
    total_partes = partes_query.count()

    # Aplicar paginación
    partes = partes_query[offset:offset + limit]

    # Serializar datos
    serializer = TrParteSerializer(partes, many=True)

    # Calcular información de paginación
    total_pages = (total_partes + limit - 1) // limit  # Redondeo hacia arriba
    has_next = page < total_pages
    has_previous = page > 1

    return Response({
        'total': total_partes,
        'partes': serializer.data,
        'usuario_id': usuario_id,
        'pagination': {
            'current_page': page,
            'total_pages': total_pages,
            'limit': limit,
            'has_next': has_next,
            'has_previous': has_previous,
            'next_page': page + 1 if has_next else None,
            'previous_page': page - 1 if has_previous else None
        },
        'filters_applied': {
            'fecha_desde': fecha_desde,
            'fecha_hasta': fecha_hasta,
            'tipo_incidencia': tipo_incidencia,
            'codigo_parte': codigo_parte,
            'buscar': buscar
        }
    })

# ==================== VISTAS PARA ARCHIVOS ====================

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def archivos_por_parte(request, parte_id):
    """
    Lista archivos de un parte específico o sube un nuevo archivo
    GET: Lista archivos del parte
    POST: Sube un nuevo archivo al parte
    """
    # Verificar que el parte existe
    try:
        parte = TrParte.objects.get(pk=parte_id)
    except TrParte.DoesNotExist:
        return Response(
            {'error': 'Parte no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )

    if request.method == 'GET':
        archivos = TrArchivo.objects.filter(
            int_idParte=parte_id,
            bool_activo=True
        ).order_by('-dt_fechaSubida')

        serializer = TrArchivoSerializer(archivos, many=True)

        return Response({
            'total': archivos.count(),
            'archivos': serializer.data,
            'parte_id': parte_id,
            'parte_codigo': parte.str_codigoParte
        })

    elif request.method == 'POST':
        # Verificar permisos: el creador del parte, admin o supervisor pueden subir archivos
        if (request.user != parte.int_idUsuario and
            request.user.str_rol not in ['ADMIN', 'SUPERVISOR']):
            return Response(
                {'error': 'No tiene permisos para subir archivos a este parte'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Crear una copia de los datos y agregar el int_idParte desde la URL
        data = request.data.copy()
        data['int_idParte'] = parte_id

        serializer = TrArchivoUploadSerializer(data=data)
        if serializer.is_valid():
            archivo_file = serializer.validated_data['archivo']

            # Procesar el archivo usando la función auxiliar
            from django.core.files.storage import default_storage

            # Generar estructura de carpetas y ruta del archivo
            upload_dir, file_path, unique_filename = generate_file_path(parte, archivo_file.name)

            # Guardar archivo
            saved_path = default_storage.save(file_path, archivo_file)

            # Obtener extensión del archivo
            file_extension = os.path.splitext(archivo_file.name)[1]

            # Crear registro en base de datos
            archivo = TrArchivo.objects.create(
                int_idParte=parte,
                int_idUsuario=request.user,
                str_nombreArchivo=archivo_file.name,
                str_extension=file_extension.lower(),
                str_ruta=saved_path,
                int_peso=archivo_file.size
            )

            # Log de la estructura de carpetas creada
            logger.info(f"Archivo guardado en estructura: {upload_dir}")
            logger.info(f"Documento usuario: {parte.int_idUsuario.str_documento}")
            logger.info(f"Incidencia: {parte.int_idIncidencia.str_codigoIncidencia if parte.int_idIncidencia else 'sin_incidencia'}")
            logger.info(f"ID Parte: {parte.int_idParte}")
            logger.info(f"Archivo original: {archivo_file.name}")
            logger.info(f"Archivo guardado como: {unique_filename}")

            response_serializer = TrArchivoSerializer(archivo)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'DELETE'])
@permission_classes([IsAuthenticated])
def archivo_detail(request, archivo_id):
    """
    Obtiene información de un archivo o lo elimina
    """
    try:
        archivo = TrArchivo.objects.select_related('int_idParte', 'int_idUsuario').get(pk=archivo_id)
    except TrArchivo.DoesNotExist:
        return Response(
            {'error': 'Archivo no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )

    if request.method == 'GET':
        serializer = TrArchivoSerializer(archivo)
        return Response(serializer.data)

    elif request.method == 'DELETE':
        # Verificar permisos: el que subió el archivo, creador del parte, admin o supervisor
        if (request.user != archivo.int_idUsuario and
            request.user != archivo.int_idParte.int_idUsuario and
            request.user.str_rol not in ['ADMIN', 'SUPERVISOR']):
            return Response(
                {'error': 'No tiene permisos para eliminar este archivo'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Eliminar archivo físico
        from django.core.files.storage import default_storage
        if default_storage.exists(archivo.str_ruta):
            default_storage.delete(archivo.str_ruta)

        # Eliminar registro de base de datos
        archivo.delete()

        return Response(
            {'message': 'Archivo eliminado correctamente'},
            status=status.HTTP_204_NO_CONTENT
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def download_archivo(request, archivo_id):
    """
    Descarga un archivo
    """
    try:
        archivo = TrArchivo.objects.select_related('int_idParte').get(pk=archivo_id)
    except TrArchivo.DoesNotExist:
        return Response(
            {'error': 'Archivo no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Verificar permisos: cualquier usuario autenticado puede descargar
    from django.core.files.storage import default_storage
    from django.http import HttpResponse, Http404

    if not default_storage.exists(archivo.str_ruta):
        return Response(
            {'error': 'Archivo físico no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )

    try:
        file_content = default_storage.open(archivo.str_ruta).read()

        response = HttpResponse(
            file_content,
            content_type='application/octet-stream'
        )
        response['Content-Disposition'] = f'attachment; filename="{archivo.str_nombreArchivo}"'
        response['Content-Length'] = archivo.int_peso

        return response
    except Exception as e:
        return Response(
            {'error': f'Error al descargar archivo: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def archivos_usuario(request):
    """
    Lista todos los archivos subidos por el usuario actual
    """
    archivos = TrArchivo.objects.select_related(
        'int_idParte', 'int_idUsuario'
    ).filter(
        int_idUsuario=request.user,
        bool_activo=True
    ).order_by('-dt_fechaSubida')

    serializer = TrArchivoSerializer(archivos, many=True)

    return Response({
        'total': archivos.count(),
        'archivos': serializer.data
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def analizar_incidente_ia(request):
    """
    API inteligente que analiza la descripción y sugiere incidencias similares
    y genera 3 propuestas de partes usando IA
    """
    from .ai_service import ai_service
    from incidencia.models import Incidencia
    from incidencia.serializers import IncidenciaSerializer

    # Validar datos de entrada
    required_fields = ['dt_fechaIncidente', 'str_horaIncidente', 'int_idUsuario', 'int_idDireccion', 'tx_descripcion', 'ia']
    for field in required_fields:
        if field not in request.data:
            return Response(
                {'error': f'Campo requerido: {field}'},
                status=status.HTTP_400_BAD_REQUEST
            )

    dt_fecha_incidente = request.data['dt_fechaIncidente']
    str_hora_incidente = request.data['str_horaIncidente']
    int_id_usuario = request.data['int_idUsuario']
    int_id_direccion = request.data['int_idDireccion']
    tx_descripcion = request.data['tx_descripcion']
    ia_tipo = request.data['ia']  # 1 para Gemini, 2 para Mistral

    # Validar tipo de IA
    if ia_tipo not in [1, 2]:
        return Response(
            {'error': 'Campo ia debe ser 1 (Gemini) o 2 (Mistral)'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Campo opcional
    int_id_incidencia = request.data.get('int_idIncidencia', None)

    # Verificar que el usuario existe
    try:
        from usuarios.models import Usuario
        usuario = Usuario.objects.get(pk=int_id_usuario)
    except Usuario.DoesNotExist:
        return Response(
            {'error': 'Usuario no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Verificar dirección (obligatoria)
    try:
        from tm_direcciones.models import TmDireccion
        direccion = TmDireccion.objects.get(pk=int_id_direccion, bool_estado=True)
        direccion_info = {
            'int_idDireccion': direccion.int_idDireccion,
            'str_codigoDireccion': direccion.str_codigoDireccion,
            'str_direccionCompleta': direccion.str_direccionCompleta
        }
    except TmDireccion.DoesNotExist:
        return Response(
            {'error': 'Dirección no encontrada o inactiva'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Verificar incidencia específica si se proporciona
    incidencia_especifica = None
    if int_id_incidencia:
        try:
            incidencia_especifica = Incidencia.objects.get(pk=int_id_incidencia, bool_estado=True)
        except Incidencia.DoesNotExist:
            return Response(
                {'error': 'Incidencia especificada no encontrada o inactiva'},
                status=status.HTTP_404_NOT_FOUND
            )

    try:
        # Obtener incidencias activas para análisis (limitadas para mejor rendimiento)
        incidencias = Incidencia.objects.filter(bool_estado=True).select_related(
            'int_idCriterio', 'int_idUsuarioCreacion'
        ).order_by('-dt_fechaRegistro')[:50]  # Limitar a las 50 más recientes

        # Convertir a formato para el servicio de IA
        incidents_data = []
        for incidencia in incidencias:
            incidents_data.append({
                'int_idIncidencia': incidencia.int_idIncidencia,
                'str_codigoIncidencia': incidencia.str_codigoIncidencia,
                'str_descripcionCorta': incidencia.str_descripcionCorta,
                'str_descripcionCompleta': incidencia.str_descripcionCompleta,
                'str_estadoIncidencia': incidencia.str_estadoIncidencia,
                'criterio': incidencia.int_idCriterio.str_descripcion if incidencia.int_idCriterio else '',
                'dt_fechaRegistro': incidencia.dt_fechaRegistro.isoformat() if incidencia.dt_fechaRegistro else None
            })

        # Analizar con IA para encontrar incidencias similares (solo si no se especificó una incidencia)
        incidencias_similares = []
        incidencia_recomendada = None

        if incidencia_especifica:
            # Si se especificó una incidencia, usarla directamente
            incidencia_recomendada = {
                'int_idIncidencia': incidencia_especifica.int_idIncidencia,
                'str_codigoIncidencia': incidencia_especifica.str_codigoIncidencia,
                'str_descripcionCorta': incidencia_especifica.str_descripcionCorta,
                'str_descripcionCompleta': incidencia_especifica.str_descripcionCompleta,
                'str_estadoIncidencia': incidencia_especifica.str_estadoIncidencia,
                'criterio': incidencia_especifica.int_idCriterio.str_descripcion if incidencia_especifica.int_idCriterio else '',
                'dt_fechaRegistro': incidencia_especifica.dt_fechaRegistro.isoformat() if incidencia_especifica.dt_fechaRegistro else None,
                'similarity_score': 1.0,
                'similarity_percentage': 100.0,
                'source': 'especificada_por_usuario'
            }
            incidencias_similares = [incidencia_recomendada]
        else:
            # Buscar incidencias similares con IA
            incidencias_similares = ai_service.find_similar_incidents(
                description=tx_descripcion,
                incidents_data=incidents_data,
                top_k=5
            )
            incidencia_recomendada = incidencias_similares[0] if incidencias_similares else None

        # Generar propuestas de partes con IA externa
        from .external_ai_service import external_ai_service
        import time

        usuario_info = {
            'str_nombres': usuario.str_nombres,
            'str_apellidos': usuario.str_apellidos,
            'str_documento': usuario.str_documento
        }

        # Medir tiempo de inicio
        start_time = time.time()

        try:
            # Usar IA externa según el tipo seleccionado con timeout personalizado
            ai_response = external_ai_service.analyze_incident_with_ai(
                ia_tipo=ia_tipo,
                descripcion=tx_descripcion,
                fecha_incidente=dt_fecha_incidente,
                hora_incidente=str_hora_incidente,
                usuario_info=usuario_info,
                direccion_info=direccion_info,
                similar_incidents=incidencias_similares
            )

            # Calcular tiempo transcurrido
            elapsed_time = time.time() - start_time
            logger.info(f"IA analysis completed in {elapsed_time:.2f} seconds")

        except Exception as ai_error:
            # Si hay error en IA, generar respuesta de fallback
            elapsed_time = time.time() - start_time
            logger.error(f"IA analysis failed after {elapsed_time:.2f} seconds: {ai_error}")

            ai_response = _generate_fallback_response(
                tx_descripcion,
                incidencias_similares,
                ia_tipo
            )

        propuestas_partes = ai_response.get('propuestas', [])
        analisis_ia = ai_response.get('analisis_ia', {})

        # Preparar respuesta
        response_data = {
            'analisis_completado': True,
            'datos_entrada': {
                'dt_fechaIncidente': dt_fecha_incidente,
                'str_horaIncidente': str_hora_incidente,
                'int_idUsuario': int_id_usuario,
                'usuario_info': {
                    'id': usuario.id,
                    'str_nombres': usuario.str_nombres,
                    'str_apellidos': usuario.str_apellidos,
                    'str_documento': usuario.str_documento
                },
                'int_idIncidencia': int_id_incidencia,
                'incidencia_especificada': incidencia_especifica is not None,
                'int_idDireccion': int_id_direccion,
                'direccion_info': direccion_info,
                'ia': ia_tipo,
                'ia_modelo': 'Gemini 2.0 Flash' if ia_tipo == 1 else 'Mistral Small',
                'tx_descripcion': tx_descripcion
            },
            'incidencias_similares': {
                'total_encontradas': len(incidencias_similares),
                'incidencias': incidencias_similares,
                'metodo_busqueda': 'especificada_por_usuario' if incidencia_especifica else 'analisis_ia'
            },
            'propuestas_partes': {
                'total_propuestas': len(propuestas_partes),
                'propuestas': propuestas_partes
            },
            'analisis_ia': analisis_ia,
            'recomendaciones': {
                'incidencia_recomendada': incidencia_recomendada,
                'confianza_analisis': 'Máxima' if incidencia_especifica else ('Alta' if incidencias_similares else 'Media'),
                'sugerencia_accion': _get_action_suggestion(incidencia_especifica, incidencias_similares),
                'usar_incidencia_especificada': incidencia_especifica is not None,
                'direccion_incluida': True
            }
        }

        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error en análisis de IA: {str(e)}")
        return Response(
            {
                'error': 'Error interno en el análisis de IA',
                'details': str(e) if settings.DEBUG else 'Contacte al administrador'
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def _get_action_suggestion(incidencia_especifica, incidencias_similares):
    """Función auxiliar para generar sugerencias de acción"""
    if incidencia_especifica:
        return f"Crear parte asociado a la incidencia especificada: {incidencia_especifica.str_codigoIncidencia}"
    elif incidencias_similares:
        return "Revisar incidencias similares antes de crear nuevo parte"
    else:
        return "Crear nuevo parte con la información proporcionada"

def _generate_fallback_response(descripcion, incidencias_similares, ia_tipo):
    """Genera una respuesta de fallback cuando la IA falla"""
    from datetime import datetime

    # Generar propuestas básicas basadas en la descripción
    propuestas = []

    # Propuesta 1: Basada en palabras clave
    if any(palabra in descripcion.lower() for palabra in ['impresora', 'imprimir', 'papel']):
        propuestas.append({
            'numero': 1,
            'titulo': 'Problema de Impresión',
            'descripcion': f'Revisar problema de impresora reportado: {descripcion[:100]}...',
            'prioridad': 'Media',
            'tiempo_estimado': '30 minutos',
            'categoria': 'Hardware - Impresión',
            'pasos_sugeridos': [
                'Verificar conexión de la impresora',
                'Revisar nivel de tinta/tóner',
                'Comprobar atascos de papel',
                'Reiniciar impresora si es necesario'
            ]
        })
    elif any(palabra in descripcion.lower() for palabra in ['internet', 'red', 'conexión', 'wifi']):
        propuestas.append({
            'numero': 1,
            'titulo': 'Problema de Conectividad',
            'descripcion': f'Resolver problema de red reportado: {descripcion[:100]}...',
            'prioridad': 'Alta',
            'tiempo_estimado': '45 minutos',
            'categoria': 'Conectividad',
            'pasos_sugeridos': [
                'Verificar cables de red',
                'Comprobar configuración IP',
                'Reiniciar router/switch',
                'Contactar proveedor de internet si es necesario'
            ]
        })
    elif any(palabra in descripcion.lower() for palabra in ['computadora', 'pc', 'sistema', 'software']):
        propuestas.append({
            'numero': 1,
            'titulo': 'Problema de Sistema',
            'descripcion': f'Atender problema de sistema reportado: {descripcion[:100]}...',
            'prioridad': 'Media',
            'tiempo_estimado': '60 minutos',
            'categoria': 'Software',
            'pasos_sugeridos': [
                'Verificar estado del sistema',
                'Revisar logs de errores',
                'Actualizar software si es necesario',
                'Reiniciar sistema'
            ]
        })
    else:
        propuestas.append({
            'numero': 1,
            'titulo': 'Incidente General',
            'descripcion': f'Atender incidente reportado: {descripcion[:100]}...',
            'prioridad': 'Media',
            'tiempo_estimado': '45 minutos',
            'categoria': 'General',
            'pasos_sugeridos': [
                'Evaluar la situación reportada',
                'Identificar causa del problema',
                'Implementar solución apropiada',
                'Verificar resolución'
            ]
        })

    # Propuesta 2: Basada en incidencias similares
    if incidencias_similares:
        incidencia_similar = incidencias_similares[0]
        propuestas.append({
            'numero': 2,
            'titulo': f'Seguimiento de Incidencia Similar',
            'descripcion': f'Crear parte relacionado con incidencia similar: {incidencia_similar.get("str_descripcionCorta", "")}',
            'prioridad': 'Media',
            'tiempo_estimado': '30 minutos',
            'categoria': 'Seguimiento',
            'pasos_sugeridos': [
                'Revisar incidencia similar existente',
                'Aplicar solución conocida',
                'Documentar diferencias si las hay',
                'Actualizar base de conocimiento'
            ]
        })

    # Propuesta 3: Genérica
    propuestas.append({
        'numero': len(propuestas) + 1,
        'titulo': 'Investigación y Diagnóstico',
        'descripcion': f'Realizar investigación detallada del problema reportado',
        'prioridad': 'Baja',
        'tiempo_estimado': '90 minutos',
        'categoria': 'Investigación',
        'pasos_sugeridos': [
            'Recopilar información adicional',
            'Realizar diagnóstico completo',
            'Consultar documentación técnica',
            'Proponer solución definitiva'
        ]
    })

    # Asegurar que tengamos exactamente 3 propuestas
    while len(propuestas) < 3:
        propuestas.append({
            'numero': len(propuestas) + 1,
            'titulo': f'Opción Alternativa {len(propuestas)}',
            'descripcion': f'Enfoque alternativo para resolver: {descripcion[:50]}...',
            'prioridad': 'Baja',
            'tiempo_estimado': '60 minutos',
            'categoria': 'Alternativo',
            'pasos_sugeridos': [
                'Evaluar enfoque alternativo',
                'Implementar solución temporal',
                'Monitorear resultados',
                'Ajustar según sea necesario'
            ]
        })

    return {
        'propuestas': propuestas[:3],  # Solo las primeras 3
        'analisis_ia': {
            'modelo_usado': f'Fallback (IA {ia_tipo} no disponible)',
            'tipo_detectado': 'Análisis básico por palabras clave',
            'palabras_clave': _extract_keywords(descripcion),
            'recomendacion_general': 'Se recomienda revisión manual debido a que el análisis de IA no estuvo disponible',
            'confianza_general': 0.60,
            'tiempo_respuesta': 'Inmediato (fallback)',
            'fallback_usado': True,
            'razon_fallback': 'Timeout o error en servicio de IA externa'
        }
    }

def _extract_keywords(descripcion):
    """Extrae palabras clave básicas de la descripción"""
    keywords_map = {
        'impresora': ['impresora', 'imprimir', 'papel', 'tinta', 'tóner'],
        'red': ['internet', 'red', 'conexión', 'wifi', 'ethernet'],
        'sistema': ['computadora', 'pc', 'sistema', 'software', 'programa'],
        'hardware': ['equipo', 'hardware', 'dispositivo', 'máquina'],
        'seguridad': ['acceso', 'contraseña', 'login', 'seguridad', 'usuario']
    }

    descripcion_lower = descripcion.lower()
    keywords_found = []

    for categoria, palabras in keywords_map.items():
        for palabra in palabras:
            if palabra in descripcion_lower:
                keywords_found.append(palabra)
                break  # Solo una palabra por categoría

    return keywords_found[:5]  # Máximo 5 palabras clave

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def consultar_ia(request):
    """
    API simple para consultar directamente a las IAs (Gemini, Mistral o ChatGPT)
    """
    from datetime import datetime

    try:
        # Validar datos de entrada
        required_fields = ['consulta', 'ia']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {'error': f'Campo requerido: {field}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        consulta = request.data['consulta']
        ia_tipo = request.data['ia']  # 1 para Gemini, 2 para Mistral, 3 para ChatGPT

        # Validar tipo de IA
        if ia_tipo not in [1, 2, 3]:
            return Response(
                {'error': 'Campo ia debe ser 1 (Gemini), 2 (Mistral) o 3 (ChatGPT)'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validar que la consulta no esté vacía
        if not consulta or not consulta.strip():
            return Response(
                {'error': 'La consulta no puede estar vacía'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Importar servicio de IA externa
        from .external_ai_service import external_ai_service

        # Realizar consulta directa a la IA
        if ia_tipo == 1:
            respuesta = external_ai_service._consultar_gemini_directo(consulta)
        elif ia_tipo == 2:
            respuesta = external_ai_service._consultar_mistral_directo(consulta)
        else:  # ia_tipo == 3
            respuesta = external_ai_service._consultar_chatgpt_directo(consulta)

        # Información del usuario para logs
        usuario_info = {
            'id': request.user.id,
            'documento': request.user.str_documento,
            'nombres': f"{request.user.str_nombres} {request.user.str_apellidos}"
        }

        # Log de la consulta
        logger.info(f"Consulta IA realizada por usuario {usuario_info['documento']}: {consulta[:100]}...")

        return Response({
            'consulta_realizada': True,
            'consulta': consulta,
            'ia_utilizada': 'Gemini 2.0 Flash' if ia_tipo == 1 else ('Mistral Small' if ia_tipo == 2 else 'ChatGPT 4o-mini'),
            'ia_tipo': ia_tipo,
            'respuesta': respuesta,
            'usuario': usuario_info,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error en consulta IA: {e}")
        return Response(
            {'error': 'Error interno del servidor', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# ==================== API PARA CERRAR PARTES ====================

@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def cerrar_parte(request, parte_id):
    """
    API para cerrar un parte (cambiar de activo a inactivo)
    Solo admins y supervisores pueden cerrar partes
    """
    try:
        # Verificar que el parte existe
        parte = TrParte.objects.get(pk=parte_id)
    except TrParte.DoesNotExist:
        return Response(
            {'error': 'Parte no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )
 

    # Verificar si el parte ya está inactivo
    if parte.int_estado == 0:
        return Response(
            {'error': 'El parte ya está cerrado (inactivo)'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Obtener datos antes del cambio para logging
    datos_anteriores = {
        'int_estado': parte.int_estado,
        'dt_fechaModificacion': parte.dt_fechaModificacion.isoformat() if parte.dt_fechaModificacion else None
    }

    # Cambiar estado a inactivo
    parte.int_estado = 0
    parte.save()

    # Obtener datos después del cambio
    datos_nuevos = {
        'int_estado': parte.int_estado,
        'dt_fechaModificacion': parte.dt_fechaModificacion.isoformat() if parte.dt_fechaModificacion else None
    }

    # Registrar en logs con el sistema mejorado
    try:
        from tm_logs.enhanced_log_service import EnhancedLogService
        log_service = EnhancedLogService()

        log_service.registrar_log_completo(
            accion='CERRAR_PARTE',
            modulo='tr_partes',
            tabla='tr_partes',
            id_registro=str(parte.int_idParte),
            usuario=request.user,
            observaciones=f'Parte {parte.str_codigoParte} cerrado por {request.user.str_rol}: {request.user.str_nombres} {request.user.str_apellidos}',
            categoria='AUDITORIA',
            datos_anteriores=datos_anteriores,
            datos_nuevos=datos_nuevos,
            campos_modificados={'int_estado': {'anterior': 1, 'nuevo': 0}},
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT'),
            metodo_http=request.method,
            endpoint=request.path,
            resultado='EXITOSO'
        )
    except Exception as log_error:
        logger.warning(f"Error al registrar log de cierre de parte: {log_error}")

    # Serializar el parte actualizado
    serializer = TrParteSerializer(parte)

    return Response({
        'message': 'Parte cerrado correctamente',
        'parte_cerrado': True,
        'estado_anterior': 'Activo',
        'estado_actual': 'Inactivo',
        'fecha_cierre': parte.dt_fechaModificacion.isoformat(),
        'cerrado_por': {
            'id': request.user.id,
            'documento': request.user.str_documento,
            'nombres': f"{request.user.str_nombres} {request.user.str_apellidos}",
            'rol': request.user.str_rol
        },
        'parte': serializer.data
    }, status=status.HTTP_200_OK)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def reabrir_parte(request, parte_id):
    """
    API para reabrir un parte (cambiar de inactivo a activo)
    Solo admins pueden reabrir partes
    """
    try:
        # Verificar que el parte existe
        parte = TrParte.objects.get(pk=parte_id)
    except TrParte.DoesNotExist:
        return Response(
            {'error': 'Parte no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Verificar permisos: solo admin puede reabrir partes
    if request.user.str_rol != 'ADMIN':
        return Response(
            {'error': 'No tiene permisos para reabrir partes. Solo administradores pueden realizar esta acción.'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Verificar si el parte ya está activo
    if parte.int_estado == 1:
        return Response(
            {'error': 'El parte ya está abierto (activo)'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Obtener datos antes del cambio para logging
    datos_anteriores = {
        'int_estado': parte.int_estado,
        'dt_fechaModificacion': parte.dt_fechaModificacion.isoformat() if parte.dt_fechaModificacion else None,
        'dt_fechaReactivacion': parte.dt_fechaReactivacion.isoformat() if parte.dt_fechaReactivacion else None
    }

    # Cambiar estado a activo y actualizar fecha de reactivación
    from django.utils import timezone
    parte.int_estado = 1
    parte.dt_fechaReactivacion = timezone.now()  # Marcar fecha de reactivación
    parte.save()

    # Obtener datos después del cambio
    datos_nuevos = {
        'int_estado': parte.int_estado,
        'dt_fechaModificacion': parte.dt_fechaModificacion.isoformat() if parte.dt_fechaModificacion else None,
        'dt_fechaReactivacion': parte.dt_fechaReactivacion.isoformat() if parte.dt_fechaReactivacion else None
    }

    # Registrar en logs con el sistema mejorado
    try:
        from tm_logs.enhanced_log_service import EnhancedLogService
        log_service = EnhancedLogService()

        log_service.registrar_log_completo(
            accion='REABRIR_PARTE',
            modulo='tr_partes',
            tabla='tr_partes',
            id_registro=str(parte.int_idParte),
            usuario=request.user,
            observaciones=f'Parte {parte.str_codigoParte} reabierto por {request.user.str_rol}: {request.user.str_nombres} {request.user.str_apellidos}',
            categoria='AUDITORIA',
            datos_anteriores=datos_anteriores,
            datos_nuevos=datos_nuevos,
            campos_modificados={
                'int_estado': {'anterior': 0, 'nuevo': 1},
                'dt_fechaReactivacion': {'anterior': datos_anteriores['dt_fechaReactivacion'], 'nuevo': datos_nuevos['dt_fechaReactivacion']}
            },
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT'),
            metodo_http=request.method,
            endpoint=request.path,
            resultado='EXITOSO'
        )
    except Exception as log_error:
        logger.warning(f"Error al registrar log de reapertura de parte: {log_error}")

    # Serializar el parte actualizado
    serializer = TrParteSerializer(parte)

    return Response({
        'message': 'Parte reabierto correctamente',
        'parte_reabierto': True,
        'estado_anterior': 'Inactivo',
        'estado_actual': 'Activo',
        'fecha_reapertura': parte.dt_fechaModificacion.isoformat(),
        'reabierto_por': {
            'id': request.user.id,
            'documento': request.user.str_documento,
            'nombres': f"{request.user.str_nombres} {request.user.str_apellidos}",
            'rol': request.user.str_rol
        },
        'parte': serializer.data
    }, status=status.HTTP_200_OK)

# ==================== VISTAS PARA RESPUESTAS VALIDADAS ====================

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def respuestas_validadas_list_create(request):
    """
    Lista todas las respuestas validadas o crea una nueva
    GET: Lista respuestas validadas con paginación
    POST: Crea una nueva respuesta validada
    """
    from .models import TrRespuestaValidada
    from .serializers import TrRespuestaValidadaSerializer, TrRespuestaValidadaCreateSerializer

    if request.method == 'GET':
        # Parámetros de paginación
        page = int(request.query_params.get('page', 1))
        limit = int(request.query_params.get('limit', 10))

        # Validar parámetros de paginación
        if page < 1:
            page = 1
        if limit < 1 or limit > 100:
            limit = 10

        # Calcular offset
        offset = (page - 1) * limit

        # Obtener respuestas validadas ordenadas del más reciente al más antiguo
        respuestas_query = TrRespuestaValidada.objects.select_related(
            'int_idParte', 'int_idParte__int_idUsuario'
        ).all().order_by('-dt_fechaValidacion')

        # Contar total
        total_respuestas = respuestas_query.count()

        # Aplicar paginación
        respuestas = respuestas_query[offset:offset + limit]

        # Serializar datos
        serializer = TrRespuestaValidadaSerializer(respuestas, many=True)

        # Calcular información de paginación
        total_pages = (total_respuestas + limit - 1) // limit
        has_next = page < total_pages
        has_previous = page > 1

        return Response({
            'total': total_respuestas,
            'respuestas_validadas': serializer.data,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'limit': limit,
                'has_next': has_next,
                'has_previous': has_previous,
                'next_page': page + 1 if has_next else None,
                'previous_page': page - 1 if has_previous else None
            }
        })

    elif request.method == 'POST':
        serializer = TrRespuestaValidadaCreateSerializer(data=request.data)
        if serializer.is_valid():
            # Verificar que el parte existe y el usuario tiene permisos
            parte_id = serializer.validated_data['int_idParte'].int_idParte
            try:
                parte = TrParte.objects.get(pk=parte_id)

                # Verificar permisos: el creador del parte, admin o supervisor pueden crear respuestas validadas
                if (request.user != parte.int_idUsuario and
                    request.user.str_rol not in ['ADMIN', 'SUPERVISOR']):
                    return Response(
                        {'error': 'No tiene permisos para crear respuestas validadas para este parte'},
                        status=status.HTTP_403_FORBIDDEN
                    )

                # Crear la respuesta validada
                respuesta_validada = serializer.save()

                # Devolver la respuesta completa
                response_serializer = TrRespuestaValidadaSerializer(respuesta_validada)
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)

            except TrParte.DoesNotExist:
                return Response(
                    {'error': 'Parte no encontrado'},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def respuesta_validada_detail(request, pk):
    """
    Obtiene, actualiza o elimina una respuesta validada específica
    """
    from .models import TrRespuestaValidada
    from .serializers import TrRespuestaValidadaSerializer, TrRespuestaValidadaCreateSerializer

    try:
        respuesta_validada = TrRespuestaValidada.objects.select_related(
            'int_idParte', 'int_idParte__int_idUsuario'
        ).get(pk=pk)
    except TrRespuestaValidada.DoesNotExist:
        return Response(
            {'error': 'Respuesta validada no encontrada'},
            status=status.HTTP_404_NOT_FOUND
        )

    if request.method == 'GET':
        serializer = TrRespuestaValidadaSerializer(respuesta_validada)
        return Response(serializer.data)

    elif request.method == 'PUT':
        # Verificar permisos: admin o supervisor pueden actualizar
        if request.user.str_rol not in ['ADMIN', 'SUPERVISOR']:
            return Response(
                {'error': 'No tiene permisos para actualizar respuestas validadas'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = TrRespuestaValidadaCreateSerializer(
            respuesta_validada,
            data=request.data,
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            # Devolver la respuesta actualizada
            response_serializer = TrRespuestaValidadaSerializer(respuesta_validada)
            return Response(response_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    elif request.method == 'DELETE':
        # Verificar permisos: solo admin puede eliminar
        if request.user.str_rol not in ['ADMIN']:
            return Response(
                {'error': 'No tiene permisos para eliminar respuestas validadas'},
                status=status.HTTP_403_FORBIDDEN
            )

        respuesta_validada.delete()
        return Response(
            {'message': 'Respuesta validada eliminada correctamente'},
            status=status.HTTP_204_NO_CONTENT
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def respuestas_validadas_por_parte(request, parte_id):
    """
    Obtiene todas las respuestas validadas de un parte específico
    """
    from .models import TrRespuestaValidada
    from .serializers import TrRespuestaValidadaSerializer

    # Verificar que el parte existe
    try:
        parte = TrParte.objects.get(pk=parte_id)
    except TrParte.DoesNotExist:
        return Response(
            {'error': 'Parte no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Obtener respuestas validadas del parte
    respuestas = TrRespuestaValidada.objects.filter(
        int_idParte=parte_id
    ).order_by('-dt_fechaValidacion')

    serializer = TrRespuestaValidadaSerializer(respuestas, many=True)

    return Response({
        'total': respuestas.count(),
        'respuestas_validadas': serializer.data,
        'parte_id': parte_id,
        'parte_codigo': parte.str_codigoParte
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def partes_mapa(request):
    """
    API optimizado para mostrar partes en el mapa
    Devuelve solo datos esenciales sin arrays complejos y sin paginación

    Filtros disponibles:
    - tipo_incidencia: ID de la incidencia
    - usuario: ID del usuario
    - criterio: ID del criterio
    - estado: 1 (activo) o 0 (inactivo)
    - fecha_desde: YYYY-MM-DD
    - fecha_hasta: YYYY-MM-DD
    """
    from .serializers import TrParteMapaSerializer
    from django.db.models import Q
    from datetime import datetime

    # Obtener todos los partes con relaciones optimizadas
    queryset = TrParte.objects.select_related(
        'int_idUsuario',
        'int_idIncidencia',
        'int_idIncidencia__int_idCriterio',
        'int_idDireccion'
    ).all()

    # Aplicar cierre automático antes de filtrar
    queryset, resultado_cierre = aplicar_cierre_automatico_y_obtener_partes(queryset)

    # Aplicar filtros
    filtros = Q()

    # Filtro por tipo de incidencia
    tipo_incidencia = request.GET.get('tipo_incidencia')
    if tipo_incidencia:
        try:
            filtros &= Q(int_idIncidencia=int(tipo_incidencia))
        except ValueError:
            return Response(
                {'error': 'tipo_incidencia debe ser un número entero'},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Filtro por usuario
    usuario = request.GET.get('usuario')
    if usuario:
        try:
            filtros &= Q(int_idUsuario=int(usuario))
        except ValueError:
            return Response(
                {'error': 'usuario debe ser un número entero'},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Filtro por criterio
    criterio = request.GET.get('criterio')
    if criterio:
        try:
            filtros &= Q(int_idIncidencia__int_idCriterio=int(criterio))
        except ValueError:
            return Response(
                {'error': 'criterio debe ser un número entero'},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Filtro por estado
    estado = request.GET.get('estado')
    if estado:
        try:
            estado_int = int(estado)
            if estado_int not in [0, 1]:
                return Response(
                    {'error': 'estado debe ser 0 (inactivo) o 1 (activo)'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            filtros &= Q(int_estado=estado_int)
        except ValueError:
            return Response(
                {'error': 'estado debe ser un número entero'},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Filtros de fecha
    fecha_desde = request.GET.get('fecha_desde')
    fecha_hasta = request.GET.get('fecha_hasta')

    if fecha_desde:
        try:
            fecha_desde_obj = datetime.strptime(fecha_desde, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
            filtros &= Q(dt_fechaIncidente__gte=fecha_desde_obj)
        except ValueError:
            return Response(
                {'error': 'fecha_desde debe tener formato YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )

    if fecha_hasta:
        try:
            fecha_hasta_obj = datetime.strptime(fecha_hasta, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
            filtros &= Q(dt_fechaIncidente__lte=fecha_hasta_obj)
        except ValueError:
            return Response(
                {'error': 'fecha_hasta debe tener formato YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Aplicar filtros al queryset
    if filtros:
        queryset = queryset.filter(filtros)

    # Ordenar por fecha de incidente (más recientes primero)
    queryset = queryset.order_by('-dt_fechaIncidente')

    # Serializar datos
    serializer = TrParteMapaSerializer(queryset, many=True)

    # Preparar estadísticas básicas
    total_partes = queryset.count()
    partes_activos = queryset.filter(int_estado=1).count()
    partes_inactivos = queryset.filter(int_estado=0).count()
    partes_con_coordenadas = queryset.filter(
        str_latitud__isnull=False,
        str_longitud__isnull=False
    ).exclude(
        str_latitud='',
        str_longitud=''
    ).count()

    return Response({
        'success': True,
        'total_partes': total_partes,
        'estadisticas': {
            'activos': partes_activos,
            'inactivos': partes_inactivos,
            'con_coordenadas': partes_con_coordenadas,
            'sin_coordenadas': total_partes - partes_con_coordenadas
        },
        'filtros_aplicados': {
            'tipo_incidencia': tipo_incidencia,
            'usuario': usuario,
            'criterio': criterio,
            'estado': estado,
            'fecha_desde': fecha_desde,
            'fecha_hasta': fecha_hasta
        },
        'partes': serializer.data
    })


# ==================== VISTA UNIFICADA PARA SERVIR ARCHIVOS ====================

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def servir_archivo_unificado(request, archivo_id):
    """
    API unificada para servir archivos: previsualización (por defecto) o descarga

    Args:
        archivo_id: ID del archivo a servir

    Query Parameters:
        download: Si está presente, fuerza la descarga del archivo

    Returns:
        HttpResponse con el archivo para previsualización (inline) o descarga

    Examples:
        GET /api/partes/archivos/123/imagen/           # Previsualización
        GET /api/partes/archivos/123/imagen/?download  # Descarga
    """
    try:
        # Obtener el archivo
        archivo = TrArchivo.objects.get(
            pk=archivo_id,
            bool_activo=True
        )

        # Verificar que el usuario tenga acceso al parte
        parte = archivo.int_idParte

        # Verificar permisos: el creador del parte, admin, supervisor o el usuario asignado pueden ver
        if (request.user != parte.int_idUsuario and
            request.user != parte.int_idUsuarioCreacion and
            request.user.str_rol not in ['ADMIN', 'SUPERVISOR']):
            raise Http404("No tiene permisos para ver este archivo")

        # Construir la ruta completa del archivo
        file_path = os.path.join(settings.MEDIA_ROOT, archivo.str_ruta)

        # Verificar que el archivo existe físicamente
        if not os.path.exists(file_path):
            raise Http404("Archivo no encontrado en el sistema de archivos")

        # Verificar que es un archivo multimedia basándose en la extensión
        extension = archivo.str_extension.lower()

        # Definir tipos de archivos multimedia soportados
        extensiones_imagen = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
        extensiones_video = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']
        extensiones_audio = ['.mp3', '.wav', '.ogg', '.aac', '.flac', '.m4a', '.wma']

        extensiones_multimedia = extensiones_imagen + extensiones_video + extensiones_audio

        if extension not in extensiones_multimedia:
            return Response(
                {'error': 'El archivo no es un archivo multimedia válido (imagen, video o audio)'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Determinar el tipo MIME
        content_type, _ = mimetypes.guess_type(file_path)
        if not content_type:
            # Fallback para tipos multimedia comunes
            mime_types = {
                # Imágenes
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.bmp': 'image/bmp',
                '.webp': 'image/webp',
                '.svg': 'image/svg+xml',
                # Videos
                '.mp4': 'video/mp4',
                '.avi': 'video/x-msvideo',
                '.mov': 'video/quicktime',
                '.wmv': 'video/x-ms-wmv',
                '.flv': 'video/x-flv',
                '.webm': 'video/webm',
                '.mkv': 'video/x-matroska',
                '.m4v': 'video/x-m4v',
                # Audios
                '.mp3': 'audio/mpeg',
                '.wav': 'audio/wav',
                '.ogg': 'audio/ogg',
                '.aac': 'audio/aac',
                '.flac': 'audio/flac',
                '.m4a': 'audio/mp4',
                '.wma': 'audio/x-ms-wma'
            }
            content_type = mime_types.get(extension, 'application/octet-stream')

        # Verificar si se solicita descarga
        es_descarga = 'download' in request.GET

        # Leer y servir el archivo
        try:
            with open(file_path, 'rb') as f:
                file_data = f.read()

            response = HttpResponse(file_data, content_type=content_type)

            # Agregar headers para optimizar la carga
            response['Cache-Control'] = 'public, max-age=3600'  # Cache por 1 hora
            response['Content-Length'] = len(file_data)

            # Configurar Content-Disposition según el modo
            if es_descarga:
                # Modo descarga: forzar descarga del archivo
                response['Content-Disposition'] = f'attachment; filename="{archivo.str_nombreArchivo}"'
                logger.info(f"Archivo descargado: {archivo.str_nombreArchivo} (ID: {archivo_id})")
            else:
                # Modo previsualización: mostrar inline en el navegador
                response['Content-Disposition'] = f'inline; filename="{archivo.str_nombreArchivo}"'
                logger.info(f"Archivo previsualizado: {archivo.str_nombreArchivo} (ID: {archivo_id})")

            # Headers adicionales para videos y audios (solo en modo previsualización)
            if not es_descarga and (extension in extensiones_video or extension in extensiones_audio):
                response['Accept-Ranges'] = 'bytes'  # Permite streaming

            return response

        except IOError:
            raise Http404("Error al leer el archivo")

    except TrArchivo.DoesNotExist:
        raise Http404("Archivo no encontrado")
    except Exception as e:
        logger.error(f"Error sirviendo archivo {archivo_id}: {str(e)}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def obtener_multimedia_parte(request, parte_id):
    """
    Obtiene la lista de archivos multimedia (imágenes, videos, audios) de un parte con URLs para mostrarlos directamente

    Args:
        parte_id: ID del parte

    Returns:
        Lista de archivos multimedia con URLs para mostrar directamente

    Example:
        GET /api/partes/123/imagenes/
    """
    try:
        # Verificar que el parte existe
        parte = TrParte.objects.get(pk=parte_id)

        # Verificar permisos
        if (request.user != parte.int_idUsuario and
            request.user != parte.int_idUsuarioCreacion and
            request.user.str_rol not in ['ADMIN', 'SUPERVISOR']):
            return Response(
                {'error': 'No tiene permisos para ver los archivos de este parte'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Obtener archivos multimedia (imágenes, videos, audios)
        extensiones_imagen = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
        extensiones_video = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']
        extensiones_audio = ['.mp3', '.wav', '.ogg', '.aac', '.flac', '.m4a', '.wma']

        extensiones_multimedia = extensiones_imagen + extensiones_video + extensiones_audio

        archivos_multimedia = TrArchivo.objects.filter(
            int_idParte=parte_id,
            bool_activo=True,
            str_extension__in=extensiones_multimedia
        ).order_by('-dt_fechaSubida')

        # Construir la respuesta con URLs para mostrar los archivos multimedia
        multimedia = []
        for archivo in archivos_multimedia:
            # Determinar el tipo de archivo
            extension = archivo.str_extension.lower()
            if extension in extensiones_imagen:
                tipo_archivo = 'imagen'
            elif extension in extensiones_video:
                tipo_archivo = 'video'
            elif extension in extensiones_audio:
                tipo_archivo = 'audio'
            else:
                tipo_archivo = 'otro'
            archivo_data = {
                'id': archivo.int_idArchivo,
                'nombre': archivo.str_nombreArchivo,
                'extension': archivo.str_extension,
                'tipo': tipo_archivo,  # 'imagen', 'video', 'audio'
                'tamaño': archivo.int_peso,  # Corregido: usar int_peso en lugar de int_tamanoArchivo
                'fecha_subida': archivo.dt_fechaSubida,
                'usuario_subida': {
                    'id': archivo.int_idUsuario.id,
                    'nombres': archivo.int_idUsuario.str_nombres,
                    'apellidos': archivo.int_idUsuario.str_apellidos
                },
                # URL unificada para previsualización (por defecto)
                'url_imagen': f"/api/partes/archivos/{archivo.int_idArchivo}/imagen/",
                # URL unificada para descarga (con parámetro download)
                'url_descarga': f"/api/partes/archivos/{archivo.int_idArchivo}/imagen/?download",
                # URLs legacy para compatibilidad
                'url_multimedia': f"/api/partes/archivos/{archivo.int_idArchivo}/imagen/",
                'url_download_legacy': f"/api/partes/archivos/{archivo.int_idArchivo}/download/"
            }
            multimedia.append(archivo_data)

        # Separar por tipos para estadísticas
        imagenes = [m for m in multimedia if m['tipo'] == 'imagen']
        videos = [m for m in multimedia if m['tipo'] == 'video']
        audios = [m for m in multimedia if m['tipo'] == 'audio']

        return Response({
            'total_multimedia': len(multimedia),
            'total_imagenes': len(imagenes),
            'total_videos': len(videos),
            'total_audios': len(audios),
            'parte_id': parte_id,
            'parte_codigo': parte.str_codigoParte,
            'multimedia': multimedia,
            # También incluir separados por tipo para facilidad del frontend
            'imagenes': imagenes,
            'videos': videos,
            'audios': audios
        })

    except TrParte.DoesNotExist:
        return Response(
            {'error': 'Parte no encontrado'},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error obteniendo multimedia del parte {parte_id}: {str(e)}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
