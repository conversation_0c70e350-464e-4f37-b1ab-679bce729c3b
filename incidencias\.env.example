# Configuración del Sistema de Incidencias
# IMPORTANTE: Copiar este archivo como .env y configurar los valores reales

# ===== CONFIGURACIÓN DE SEGURIDAD =====
SECRET_KEY=tu-clave-secreta-django-aqui
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# ===== CONFIGURACIÓN DE BASE DE DATOS =====
DB_ENGINE=django.db.backends.mysql
DB_NAME=db_incidencias
DB_USER=root
DB_PASSWORD=tu_password_mysql
DB_HOST=localhost
DB_PORT=3306
DB_CHARSET=utf8mb4

# ===== CONFIGURACIÓN JWT =====
JWT_ACCESS_TOKEN_LIFETIME_MINUTES=720
JWT_REFRESH_TOKEN_LIFETIME_DAYS=7
JWT_ROTATE_REFRESH_TOKENS=True

# ===== CONFIGURACIÓN CORS =====
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://localhost:5174,http://localhost:5175
CORS_ALLOW_CREDENTIALS=True

# ===== CONFIGURACIÓN DE IDIOMA Y ZONA HORARIA =====
LANGUAGE_CODE=es-pe
TIME_ZONE=America/Lima
USE_I18N=True
USE_TZ=True

# ===== CONFIGURACIÓN DE ARCHIVOS ESTÁTICOS =====
STATIC_URL=/static/
MEDIA_URL=/media/

# ===== CONFIGURACIÓN DE API =====
API_BASE_URL=http://localhost:8000/api/
API_VERSION=v1

# ===== CONFIGURACIÓN DE IAs EXTERNAS =====
# Google Gemini API Key - Obtener en: https://ai.google.dev/
GEMINI_API_KEY=tu_gemini_api_key_aqui

# Mistral AI API Key - Obtener en: https://console.mistral.ai/
MISTRAL_API_KEY=tu_mistral_api_key_aqui

# OpenAI ChatGPT API Key - Obtener en: https://platform.openai.com/api-keys
OPENAI_API_KEY=tu_openai_api_key_aqui
