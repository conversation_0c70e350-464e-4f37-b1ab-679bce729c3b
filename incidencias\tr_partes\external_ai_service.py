"""
Servicio de IA externa usando Gemini y Mistral para análisis de incidentes
"""

import requests
import json
import logging
import os
from datetime import datetime
from django.conf import settings
from decouple import config

# Configurar logging
logger = logging.getLogger(__name__)

class ExternalAIService:
    def __init__(self):
        # Configuración para Gemini (IA 1) - desde variables de entorno
        self.gemini_api_key = config('GEMINI_API_KEY', default='')
        if not self.gemini_api_key:
            logger.warning("GEMINI_API_KEY no configurada en variables de entorno")

        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.gemini_api_key}"
        self.gemini_headers = {
            "Content-Type": "application/json"
        }

        # Configuración para Mistral (IA 2) - desde variables de entorno
        self.mistral_api_key = config('MISTRAL_API_KEY', default='')
        if not self.mistral_api_key:
            logger.warning("MISTRAL_API_KEY no configurada en variables de entorno")

        self.mistral_url = "https://api.mistral.ai/v1/chat/completions"
        self.mistral_headers = {
            "Authorization": f"Bearer {self.mistral_api_key}",
            "Content-Type": "application/json"
        }

        # Configuración para OpenAI ChatGPT (IA 3) - desde variables de entorno
        self.openai_api_key = config('OPENAI_API_KEY', default='')
        if not self.openai_api_key:
            logger.warning("OPENAI_API_KEY no configurada en variables de entorno")

        self.openai_url = "https://api.openai.com/v1/chat/completions"
        self.openai_headers = {
            "Authorization": f"Bearer {self.openai_api_key}",
            "Content-Type": "application/json"
        }

        # Rutas de archivos de prompts
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.gemini_prompt_file = os.path.join(self.base_dir, 'prompt_gemini.txt')
        self.mistral_prompt_file = os.path.join(self.base_dir, 'prompt_mistral.txt')
        self.chatgpt_prompt_file = os.path.join(self.base_dir, 'prompt_chatgpt.txt')
        self.consulta_ia_prompt_file = os.path.join(self.base_dir, 'prompt_consulta_ia.txt')

    def _load_prompt(self, prompt_file):
        """Cargar prompt desde archivo"""
        try:
            with open(prompt_file, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except FileNotFoundError:
            logger.error(f"Archivo de prompt no encontrado: {prompt_file}")
            return None
        except Exception as e:
            logger.error(f"Error al leer archivo de prompt {prompt_file}: {e}")
            return None
    
    def analyze_incident_with_ai(self, ia_tipo, descripcion, fecha_incidente, hora_incidente, 
                                usuario_info, direccion_info, similar_incidents=None):
        """
        Analizar incidente usando IA externa (Gemini, Mistral o ChatGPT)

        Args:
            ia_tipo (int): 1 para Gemini, 2 para Mistral, 3 para ChatGPT
            descripcion (str): Descripción del incidente
            fecha_incidente (str): Fecha del incidente
            hora_incidente (str): Hora del incidente
            usuario_info (dict): Información del usuario
            direccion_info (dict): Información de la dirección
            similar_incidents (list): Lista de incidencias similares
        
        Returns:
            dict: Respuesta con propuestas generadas por IA
        """
        try:
            if ia_tipo == 1:
                return self._analyze_with_gemini(
                    descripcion, fecha_incidente, hora_incidente,
                    usuario_info, direccion_info, similar_incidents
                )
            elif ia_tipo == 2:
                return self._analyze_with_mistral(
                    descripcion, fecha_incidente, hora_incidente,
                    usuario_info, direccion_info, similar_incidents
                )
            elif ia_tipo == 3:
                return self._analyze_with_chatgpt(
                    descripcion, fecha_incidente, hora_incidente,
                    usuario_info, direccion_info, similar_incidents
                )
            else:
                raise ValueError("Tipo de IA no válido")
                
        except Exception as e:
            logger.error(f"Error en análisis con IA externa: {e}")
            return self._generate_fallback_response(
                descripcion, fecha_incidente, hora_incidente, 
                usuario_info, direccion_info
            )
    
    def _analyze_with_gemini(self, descripcion, fecha_incidente, hora_incidente,
                           usuario_info, direccion_info, similar_incidents):
        """Análisis usando Gemini AI"""

        # Verificar que la API key esté configurada
        if not self.gemini_api_key:
            logger.error("GEMINI_API_KEY no configurada")
            raise Exception("GEMINI_API_KEY no configurada en variables de entorno")

        # Construir contexto para Gemini
        contexto = self._build_context(
            descripcion, fecha_incidente, hora_incidente,
            usuario_info, direccion_info, similar_incidents
        )

        # Cargar prompt desde archivo
        prompt_template = self._load_prompt(self.gemini_prompt_file)
        if not prompt_template:
            # Fallback si no se puede cargar el archivo
            prompt_template = """
Eres un asistente especializado en gestión de incidentes municipales.
Analiza el siguiente incidente y genera exactamente 3 propuestas de partes profesionales.

CONTEXTO DEL INCIDENTE:
{contexto}

Responde en formato JSON con propuestas y análisis.
"""

        # Formatear el prompt con el contexto
        prompt = prompt_template.format(contexto=contexto)

        payload = {
            "contents": [
                {
                    "parts": [
                        {"text": prompt}
                    ]
                }
            ]
        }

        try:
            response = requests.post(
                self.gemini_url,
                headers=self.gemini_headers,
                data=json.dumps(payload),
                timeout=20
            )
            
            logger.info(f"Gemini response status: {response.status_code}")
            
            if response.status_code == 200:
                respuesta_json = response.json()
                content = respuesta_json['candidates'][0]['content']['parts'][0]['text']
                
                # Intentar parsear JSON de la respuesta
                try:
                    # Limpiar la respuesta si viene con markdown
                    if "```json" in content:
                        content = content.split("```json")[1].split("```")[0]
                    elif "```" in content:
                        content = content.split("```")[1].split("```")[0]
                    
                    ai_response = json.loads(content.strip())
                    return self._format_ai_response(ai_response, "Gemini", descripcion, 
                                                  fecha_incidente, hora_incidente, 
                                                  usuario_info, direccion_info)
                except json.JSONDecodeError:
                    logger.warning("No se pudo parsear JSON de Gemini, usando respuesta de texto")
                    return self._parse_text_response(content, "Gemini", descripcion,
                                                   fecha_incidente, hora_incidente,
                                                   usuario_info, direccion_info)
            else:
                logger.error(f"Error en Gemini API: {response.status_code} - {response.text}")
                raise Exception(f"Error en Gemini API: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error al consultar Gemini: {e}")
            raise
    
    def _analyze_with_mistral(self, descripcion, fecha_incidente, hora_incidente,
                            usuario_info, direccion_info, similar_incidents):
        """Análisis usando Mistral AI"""

        # Verificar que la API key esté configurada
        if not self.mistral_api_key:
            logger.error("MISTRAL_API_KEY no configurada")
            raise Exception("MISTRAL_API_KEY no configurada en variables de entorno")

        # Construir contexto para Mistral
        contexto = self._build_context(
            descripcion, fecha_incidente, hora_incidente,
            usuario_info, direccion_info, similar_incidents
        )

        system_prompt = """Eres un asistente especializado en gestión de incidentes municipales.
Analiza incidentes y genera propuestas de partes profesionales en formato JSON."""

        # Cargar prompt desde archivo
        user_prompt_template = self._load_prompt(self.mistral_prompt_file)
        if not user_prompt_template:
            # Fallback si no se puede cargar el archivo
            user_prompt_template = """
Analiza este incidente y genera exactamente 3 propuestas de partes:

CONTEXTO:
{contexto}

Responde SOLO en formato JSON válido con propuestas y análisis.
"""

        # Formatear el prompt con el contexto
        user_prompt = user_prompt_template.format(contexto=contexto)

        payload = {
            "model": "mistral-small",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "temperature": 0.7
        }

        try:
            response = requests.post(
                self.mistral_url,
                headers=self.mistral_headers,
                json=payload,
                timeout=20
            )
            
            logger.info(f"Mistral response status: {response.status_code}")
            
            if response.status_code == 200:
                respuesta_json = response.json()
                content = respuesta_json["choices"][0]["message"]["content"]
                
                # Intentar parsear JSON de la respuesta
                try:
                    # Limpiar la respuesta si viene con markdown
                    if "```json" in content:
                        content = content.split("```json")[1].split("```")[0]
                    elif "```" in content:
                        content = content.split("```")[1].split("```")[0]
                    
                    ai_response = json.loads(content.strip())
                    return self._format_ai_response(ai_response, "Mistral", descripcion,
                                                  fecha_incidente, hora_incidente,
                                                  usuario_info, direccion_info)
                except json.JSONDecodeError:
                    logger.warning("No se pudo parsear JSON de Mistral, usando respuesta de texto")
                    return self._parse_text_response(content, "Mistral", descripcion,
                                                   fecha_incidente, hora_incidente,
                                                   usuario_info, direccion_info)
            else:
                logger.error(f"Error en Mistral API: {response.status_code} - {response.text}")
                raise Exception(f"Error en Mistral API: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error al consultar Mistral: {e}")
            raise

    def _analyze_with_chatgpt(self, descripcion, fecha_incidente, hora_incidente,
                             usuario_info, direccion_info, similar_incidents):
        """Analizar incidente con ChatGPT (OpenAI)"""

        # Verificar que la API key esté configurada
        if not self.openai_api_key:
            logger.error("OPENAI_API_KEY no configurada")
            raise Exception("OPENAI_API_KEY no configurada en variables de entorno")

        # Construir contexto
        contexto = self._build_context(descripcion, fecha_incidente, hora_incidente,
                                     usuario_info, direccion_info, similar_incidents)

        # Cargar prompt desde archivo
        user_prompt_template = self._load_prompt(self.chatgpt_prompt_file)
        if not user_prompt_template:
            # Fallback si no se puede cargar el archivo
            user_prompt_template = """
Analiza este incidente y genera exactamente 3 propuestas de partes:

CONTEXTO:
{contexto}

Responde SOLO en formato JSON válido con propuestas y análisis.
"""

        # Formatear el prompt con el contexto
        user_prompt = user_prompt_template.format(contexto=contexto)

        payload = {
            "model": "gpt-4o-mini",
            "messages": [
                {
                    "role": "system",
                    "content": "Eres un experto en gestión de incidentes municipales. Analiza problemas técnicos y administrativos, genera propuestas profesionales y responde únicamente en formato JSON válido."
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 2000
        }

        try:
            response = requests.post(
                self.openai_url,
                headers=self.openai_headers,
                json=payload,
                timeout=20
            )

            if response.status_code == 200:
                response_data = response.json()
                content = response_data['choices'][0]['message']['content']

                # Intentar parsear JSON de la respuesta
                try:
                    # Limpiar la respuesta si viene con markdown
                    if "```json" in content:
                        content = content.split("```json")[1].split("```")[0]
                    elif "```" in content:
                        content = content.split("```")[1].split("```")[0]

                    ai_response = json.loads(content.strip())
                    return self._format_ai_response(ai_response, "ChatGPT", descripcion,
                                                  fecha_incidente, hora_incidente,
                                                  usuario_info, direccion_info)
                except json.JSONDecodeError:
                    logger.warning("No se pudo parsear JSON de ChatGPT, usando respuesta de texto")
                    return self._parse_text_response(content, "ChatGPT", descripcion,
                                                   fecha_incidente, hora_incidente,
                                                   usuario_info, direccion_info)
            else:
                logger.error(f"Error en OpenAI API: {response.status_code} - {response.text}")
                raise Exception(f"Error en OpenAI API: {response.status_code}")

        except Exception as e:
            logger.error(f"Error al consultar ChatGPT: {e}")
            raise

    def _build_context(self, descripcion, fecha_incidente, hora_incidente,
                      usuario_info, direccion_info, similar_incidents):
        """Construir contexto para las IAs"""
        
        # Información del usuario
        usuario_nombre = "Usuario no especificado"
        if usuario_info:
            nombres = usuario_info.get('str_nombres', '')
            apellidos = usuario_info.get('str_apellidos', '')
            documento = usuario_info.get('str_documento', '')
            if nombres and apellidos:
                usuario_nombre = f"{nombres} {apellidos}"
                if documento:
                    usuario_nombre += f" (Doc: {documento})"
        
        # Información de dirección
        direccion_texto = "Ubicación no especificada"
        if direccion_info:
            direccion_texto = direccion_info.get('str_direccionCompleta', 
                                               direccion_info.get('str_codigoDireccion', 'Ubicación no especificada'))
        
        # Formatear fecha
        try:
            fecha_obj = datetime.fromisoformat(fecha_incidente.replace('Z', '+00:00'))
            fecha_formateada = fecha_obj.strftime("%d de %B de %Y")
        except:
            fecha_formateada = fecha_incidente
        
        contexto = f"""
FECHA: {fecha_formateada}
HORA: {hora_incidente}
USUARIO: {usuario_nombre}
UBICACIÓN: {direccion_texto}
DESCRIPCIÓN: {descripcion}
"""
        
        if similar_incidents:
            contexto += f"\nINCIDENCIAS SIMILARES ENCONTRADAS: {len(similar_incidents)}"
            for inc in similar_incidents[:2]:
                contexto += f"\n- {inc.get('str_codigoIncidencia', 'N/A')}: {inc.get('str_descripcionCorta', 'N/A')}"
        
        return contexto
    
    def _format_ai_response(self, ai_response, ai_name, descripcion, fecha_incidente, 
                          hora_incidente, usuario_info, direccion_info):
        """Formatear respuesta de IA al formato esperado"""
        
        propuestas_formateadas = []
        
        try:
            propuestas = ai_response.get('propuestas', [])
            
            for i, propuesta in enumerate(propuestas[:3], 1):
                propuesta_formateada = {
                    'suggestion_number': i,
                    'description': propuesta.get('descripcion', f'Propuesta {i} generada por {ai_name}'),
                    'priority': propuesta.get('prioridad', 'Media'),
                    'type': propuesta.get('tipo', 'General'),
                    'confidence_score': propuesta.get('confianza', 0.85),
                    'action_suggested': propuesta.get('accion_sugerida', 'Revisión general'),
                    'ai_generated': True,
                    'ai_model': ai_name,
                    'direccion_info': direccion_info,
                    'direccion_incluida': direccion_info is not None
                }
                propuestas_formateadas.append(propuesta_formateada)
            
            # Asegurar que tenemos 3 propuestas
            while len(propuestas_formateadas) < 3:
                num = len(propuestas_formateadas) + 1
                propuestas_formateadas.append({
                    'suggestion_number': num,
                    'description': f'Propuesta {num} generada por {ai_name} - Requiere revisión adicional del incidente reportado.',
                    'priority': 'Media',
                    'type': 'General',
                    'confidence_score': 0.70,
                    'action_suggested': 'Revisión y análisis adicional',
                    'ai_generated': True,
                    'ai_model': ai_name,
                    'direccion_info': direccion_info,
                    'direccion_incluida': direccion_info is not None
                })
            
            analisis = ai_response.get('analisis', {})
            
            return {
                'propuestas': propuestas_formateadas,
                'analisis_ia': {
                    'modelo_usado': ai_name,
                    'tipo_detectado': analisis.get('tipo_detectado', 'General'),
                    'palabras_clave': analisis.get('palabras_clave', []),
                    'recomendacion_general': analisis.get('recomendacion_general', 'Proceder según protocolo estándar'),
                    'confianza_general': 0.85
                }
            }
            
        except Exception as e:
            logger.error(f"Error al formatear respuesta de {ai_name}: {e}")
            return self._generate_fallback_response(descripcion, fecha_incidente, hora_incidente, 
                                                  usuario_info, direccion_info, ai_name)
    
    def _parse_text_response(self, content, ai_name, descripcion, fecha_incidente, 
                           hora_incidente, usuario_info, direccion_info):
        """Parsear respuesta de texto cuando no es JSON válido"""
        
        # Generar propuestas básicas basadas en el texto de respuesta
        propuestas = []
        
        for i in range(3):
            propuesta = {
                'suggestion_number': i + 1,
                'description': f'Propuesta {i + 1} basada en análisis de {ai_name}: {content[:200]}...',
                'priority': 'Media',
                'type': 'General',
                'confidence_score': 0.75,
                'action_suggested': 'Revisión según recomendaciones de IA',
                'ai_generated': True,
                'ai_model': ai_name,
                'direccion_info': direccion_info,
                'direccion_incluida': direccion_info is not None
            }
            propuestas.append(propuesta)
        
        return {
            'propuestas': propuestas,
            'analisis_ia': {
                'modelo_usado': ai_name,
                'tipo_detectado': 'General',
                'palabras_clave': [],
                'recomendacion_general': content[:300] + '...' if len(content) > 300 else content,
                'confianza_general': 0.75,
                'respuesta_completa': content
            }
        }
    
    def _generate_fallback_response(self, descripcion, fecha_incidente, hora_incidente, 
                                  usuario_info, direccion_info, ai_name="Sistema"):
        """Generar respuesta de fallback cuando falla la IA"""
        
        propuestas = []
        
        for i in range(3):
            propuesta = {
                'suggestion_number': i + 1,
                'description': f'Propuesta {i + 1} - Incidente reportado requiere atención. Descripción: {descripcion[:100]}...',
                'priority': 'Media',
                'type': 'General',
                'confidence_score': 0.60,
                'action_suggested': 'Revisión manual requerida',
                'ai_generated': False,
                'ai_model': f'{ai_name} (Fallback)',
                'direccion_info': direccion_info,
                'direccion_incluida': direccion_info is not None
            }
            propuestas.append(propuesta)
        
        return {
            'propuestas': propuestas,
            'analisis_ia': {
                'modelo_usado': f'{ai_name} (Fallback)',
                'tipo_detectado': 'General',
                'palabras_clave': [],
                'recomendacion_general': 'Se requiere revisión manual debido a error en IA',
                'confianza_general': 0.60,
                'error': 'Fallback activado por error en IA externa'
            }
        }

    def _consultar_gemini_directo(self, consulta):
        """Consulta directa a Gemini con prompt especializado en seguridad ciudadana"""

        # Verificar que la API key esté configurada
        if not self.gemini_api_key:
            logger.error("GEMINI_API_KEY no configurada")
            return {
                'exito': False,
                'error': 'GEMINI_API_KEY no configurada en variables de entorno',
                'respuesta': 'Lo siento, la configuración de Gemini no está disponible en este momento.'
            }

        # Cargar prompt especializado desde archivo
        prompt_template = self._load_prompt(self.consulta_ia_prompt_file)
        if not prompt_template:
            # Fallback si no se puede cargar el archivo
            prompt_template = """
Eres un experto en seguridad ciudadana y gestión municipal. Responde de forma clara y profesional.

CONSULTA DEL USUARIO:
{consulta}
"""

        # Formatear el prompt con la consulta
        prompt_completo = prompt_template.format(consulta=consulta)

        payload = {
            "contents": [
                {
                    "parts": [
                        {"text": prompt_completo}
                    ]
                }
            ]
        }

        try:
            response = requests.post(
                self.gemini_url,
                headers=self.gemini_headers,
                data=json.dumps(payload),
                timeout=20
            )

            logger.info(f"Gemini consulta directa - Status: {response.status_code}")

            if response.status_code == 200:
                respuesta_json = response.json()
                content = respuesta_json['candidates'][0]['content']['parts'][0]['text']
                return {
                    'exito': True,
                    'respuesta': content,
                    'modelo': 'Gemini 2.0 Flash',
                    'tokens_usados': len(consulta.split()) + len(content.split())
                }
            else:
                logger.error(f"Error en Gemini API: {response.status_code} - {response.text}")
                return {
                    'exito': False,
                    'error': f"Error en Gemini API: {response.status_code}",
                    'respuesta': f"Lo siento, no pude procesar tu consulta en este momento. Error: {response.status_code}"
                }

        except Exception as e:
            logger.error(f"Error al consultar Gemini: {e}")
            return {
                'exito': False,
                'error': str(e),
                'respuesta': "Lo siento, no pude procesar tu consulta en este momento debido a un error de conexión."
            }

    def _consultar_mistral_directo(self, consulta):
        """Consulta directa a Mistral con prompt especializado en seguridad ciudadana"""

        # Verificar que la API key esté configurada
        if not self.mistral_api_key:
            logger.error("MISTRAL_API_KEY no configurada")
            return {
                'exito': False,
                'error': 'MISTRAL_API_KEY no configurada en variables de entorno',
                'respuesta': 'Lo siento, la configuración de Mistral no está disponible en este momento.'
            }

        # Cargar prompt especializado desde archivo
        prompt_template = self._load_prompt(self.consulta_ia_prompt_file)
        if not prompt_template:
            # Fallback si no se puede cargar el archivo
            prompt_template = """
Eres un experto en seguridad ciudadana y gestión municipal. Responde de forma clara y profesional.

CONSULTA DEL USUARIO:
{consulta}
"""

        # Formatear el prompt con la consulta
        prompt_completo = prompt_template.format(consulta=consulta)

        payload = {
            "model": "mistral-small",
            "messages": [
                {"role": "system", "content": "Eres un experto en seguridad ciudadana y gestión municipal especializado en resolver problemas técnicos, administrativos y operativos en entidades públicas."},
                {"role": "user", "content": prompt_completo}
            ],
            "temperature": 0.7
        }

        try:
            response = requests.post(
                self.mistral_url,
                headers=self.mistral_headers,
                json=payload,
                timeout=20
            )

            logger.info(f"Mistral consulta directa - Status: {response.status_code}")

            if response.status_code == 200:
                respuesta_json = response.json()
                content = respuesta_json["choices"][0]["message"]["content"]
                return {
                    'exito': True,
                    'respuesta': content,
                    'modelo': 'Mistral Small',
                    'tokens_usados': respuesta_json.get("usage", {}).get("total_tokens", 0)
                }
            else:
                logger.error(f"Error en Mistral API: {response.status_code} - {response.text}")
                return {
                    'exito': False,
                    'error': f"Error en Mistral API: {response.status_code}",
                    'respuesta': f"Lo siento, no pude procesar tu consulta en este momento. Error: {response.status_code}"
                }

        except Exception as e:
            logger.error(f"Error al consultar Mistral: {e}")
            return {
                'exito': False,
                'error': str(e),
                'respuesta': "Lo siento, no pude procesar tu consulta en este momento debido a un error de conexión."
            }

    def _consultar_chatgpt_directo(self, consulta):
        """Consulta directa a ChatGPT con prompt especializado en seguridad ciudadana"""

        # Verificar que la API key esté configurada
        if not self.openai_api_key:
            logger.error("OPENAI_API_KEY no configurada")
            return {
                'exito': False,
                'error': 'OPENAI_API_KEY no configurada en variables de entorno',
                'respuesta': 'Lo siento, la configuración de ChatGPT no está disponible en este momento.'
            }

        # Cargar prompt especializado desde archivo
        prompt_template = self._load_prompt(self.consulta_ia_prompt_file)
        if not prompt_template:
            # Fallback si no se puede cargar el archivo
            prompt_template = """
Eres un experto en seguridad ciudadana y gestión municipal. Responde de forma clara y profesional.

CONSULTA DEL USUARIO:
{consulta}
"""

        # Formatear el prompt con la consulta
        prompt_completo = prompt_template.format(consulta=consulta)

        payload = {
            "model": "gpt-4o-mini",
            "messages": [
                {"role": "system", "content": "Eres un experto en seguridad ciudadana y gestión municipal especializado en resolver problemas técnicos, administrativos y operativos en entidades públicas."},
                {"role": "user", "content": prompt_completo}
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }

        try:
            response = requests.post(
                self.openai_url,
                headers=self.openai_headers,
                json=payload,
                timeout=20
            )

            if response.status_code == 200:
                response_data = response.json()
                content = response_data['choices'][0]['message']['content']

                return {
                    'exito': True,
                    'respuesta': content.strip(),
                    'modelo_usado': 'gpt-4o-mini',
                    'tokens_usados': response_data.get('usage', {}).get('total_tokens', 0)
                }
            else:
                logger.error(f"Error en OpenAI API: {response.status_code} - {response.text}")
                return {
                    'exito': False,
                    'error': f"Error en OpenAI API: {response.status_code}",
                    'respuesta': f"Lo siento, no pude procesar tu consulta en este momento. Error: {response.status_code}"
                }

        except Exception as e:
            logger.error(f"Error al consultar ChatGPT: {e}")
            return {
                'exito': False,
                'error': str(e),
                'respuesta': "Lo siento, no pude procesar tu consulta en este momento debido a un error de conexión."
            }

# Instancia global del servicio
external_ai_service = ExternalAIService()
